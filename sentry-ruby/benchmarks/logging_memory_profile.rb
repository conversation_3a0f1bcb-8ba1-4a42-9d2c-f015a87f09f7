#!/usr/bin/env ruby
# frozen_string_literal: true

# Sentry Logging Memory Profile Benchmark
#
# This script measures memory usage when calling Sentry.logger.info multiple times
# and generates a comprehensive HTML report with memory allocation analysis.
#
# Usage: ruby benchmarks/logging_memory_profile.rb

require 'benchmark/ipsa'
require 'fileutils'
require "sentry-ruby"
require "sentry/benchmarks/benchmark_transport"

# Helper methods for formatting and report generation
def format_bytes(bytes)
  return "0 B" if bytes == 0

  units = ['B', 'KB', 'MB', 'GB']
  size = bytes.to_f
  unit_index = 0

  while size >= 1024 && unit_index < units.length - 1
    size /= 1024
    unit_index += 1
  end

  "#{size.round(2)} #{units[unit_index]}"
end

def print_memory_summary(main_report, baseline_report)
  puts "Memory Summary:"
  puts "  Total allocated objects: #{main_report.total_allocated}"
  puts "  Total retained objects: #{main_report.total_retained}"
  puts "  Total allocated memory: #{format_bytes(main_report.total_allocated_memsize)}"
  puts "  Total retained memory: #{format_bytes(main_report.total_retained_memsize)}"
  puts
end

# Configuration
ITERATIONS = ENV.fetch('ITERATIONS', '1000').to_i
OUTPUT_DIR = 'tmp'
REPORT_FILE = File.join(OUTPUT_DIR, 'logging_memory_profile.html')

puts "=== Sentry Logging Memory Profile Benchmark ==="
puts "Iterations: #{ITERATIONS}"
puts "Output: #{REPORT_FILE}"
puts

# Ensure output directory exists
FileUtils.mkdir_p(OUTPUT_DIR)

# Configure Sentry for benchmarking
Sentry.init do |config|
  config.dsn = "dummy://12345:<EMAIL>:3000/sentry/42"
  config.transport.transport_class = Sentry::BenchmarkTransport
  config.enable_logs = true
  config.sdk_logger = ::Logger.new(nil) # Disable SDK logging to avoid noise
  config.breadcrumbs_logger = []
  config.send_client_reports = false
  config.auto_session_tracking = false
  config.enable_backpressure_handling = false
end

# Verify Sentry logger is available
unless Sentry.logger
  puts "ERROR: Sentry.logger is not available. Make sure enable_logs is true."
  exit 1
end

puts "✓ Sentry configured with BenchmarkTransport"
puts "✓ Logging enabled: #{Sentry.configuration.enable_logs}"
puts

# Warm up - ensure all classes are loaded
puts "Warming up..."
10.times { |i| Sentry.logger.info("Warmup message #{i}", iteration: i) }
puts "✓ Warmup complete"
puts

# Memory profiling
puts "Starting memory profiling..."

# Baseline memory measurement
baseline_report = MemoryProfiler.report do
  # Just initialize without doing anything
  nil
end

# Main profiling run
main_report = MemoryProfiler.report do
  ITERATIONS.times do |i|
    # Vary the log messages to simulate real usage
    case i % 4
    when 0
      Sentry.logger.info("User action completed",
        user_id: i,
        action: "login",
        timestamp: Time.now.to_f,
        session_id: "session_#{i % 100}"
      )
    when 1
      Sentry.logger.warn("API rate limit approaching",
        endpoint: "/api/users",
        current_requests: i * 2,
        limit: 1000,
        user_agent: "TestClient/1.0"
      )
    when 2
      Sentry.logger.error("Database query slow",
        query_time: (i % 10) * 0.1,
        table: "users",
        query_id: "query_#{i}",
        affected_rows: i % 50
      )
    when 3
      Sentry.logger.debug("Cache operation",
        operation: "set",
        key: "user_#{i}",
        ttl: 3600,
        size_bytes: i * 10
      )
    end

    # Add some variability to simulate real-world usage
    if i % 100 == 0
      Sentry.logger.fatal("Critical system error",
        error_code: "SYS_#{i}",
        component: "database",
        severity: "critical"
      )
    end
  end
end

puts "✓ Memory profiling complete"
puts

# Get transport statistics
transport = Sentry.get_current_client.transport
transport_stats = transport.memory_usage_summary

puts "Transport Statistics:"
puts "  Total events: #{transport_stats[:events]}"
puts "  Total envelopes: #{transport_stats[:envelopes]}"
puts "  Total log events: #{transport_stats[:log_events]}"
puts

# Generate comprehensive HTML report
puts "Generating HTML report..."

html_content = generate_html_report(main_report, baseline_report, transport_stats, ITERATIONS)

File.write(REPORT_FILE, html_content)

puts "✓ HTML report generated: #{REPORT_FILE}"
puts

# Print summary to console
print_memory_summary(main_report, baseline_report)

puts "=== Benchmark Complete ==="
puts "Open #{REPORT_FILE} in your browser to view the detailed memory analysis."

def generate_html_report(main_report, baseline_report, transport_stats, iterations)
  <<~HTML
    <!DOCTYPE html>
    <html>
    <head>
        <title>Sentry Logging Memory Profile Report</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9f4ff; border-radius: 3px; min-width: 200px; }
            .memory-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .memory-table th, .memory-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .memory-table th { background-color: #f2f2f2; font-weight: bold; }
            .memory-table tr:nth-child(even) { background-color: #f9f9f9; }
            .chart-container { margin: 20px 0; padding: 15px; background: #fafafa; border-radius: 5px; }
            pre { background: #f8f8f8; padding: 15px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .code-location { font-family: monospace; font-size: 11px; color: #666; }
            .progress-bar { width: 100%; background-color: #e0e0e0; border-radius: 3px; overflow: hidden; }
            .progress-fill { height: 20px; background-color: #4CAF50; text-align: center; line-height: 20px; color: white; font-size: 12px; }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <div class="header">
            <h1>🔍 Sentry Logging Memory Profile Report</h1>
            <p><strong>Generated:</strong> #{Time.now.strftime('%Y-%m-%d %H:%M:%S %Z')}</p>
            <p><strong>Iterations:</strong> #{iterations.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}</p>
            <p><strong>Ruby Version:</strong> #{RUBY_VERSION}</p>
            <p><strong>Sentry Version:</strong> #{Sentry::VERSION}</p>
        </div>

        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="metric">
                <strong>Total Allocated Objects:</strong><br>
                #{main_report.total_allocated.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}
            </div>
            <div class="metric">
                <strong>Total Retained Objects:</strong><br>
                #{main_report.total_retained.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}
            </div>
            <div class="metric">
                <strong>Memory Allocated:</strong><br>
                #{format_bytes(main_report.total_allocated_memsize)}
            </div>
            <div class="metric">
                <strong>Memory Retained:</strong><br>
                #{format_bytes(main_report.total_retained_memsize)}
            </div>
            <div class="metric">
                <strong>Retention Rate:</strong><br>
                #{((main_report.total_retained.to_f / main_report.total_allocated * 100).round(2))}%
            </div>
            <div class="metric">
                <strong>Avg Memory/Iteration:</strong><br>
                #{format_bytes(main_report.total_allocated_memsize / iterations)}
            </div>
        </div>

        <div class="section">
            <h2>🚀 Transport Statistics</h2>
            <table class="memory-table">
                <tr><th>Metric</th><th>Count</th><th>Memory Usage</th></tr>
                <tr><td>Events Captured</td><td>#{transport_stats[:events]}</td><td>#{format_bytes(transport_stats[:events_memory])}</td></tr>
                <tr><td>Envelopes Created</td><td>#{transport_stats[:envelopes]}</td><td>#{format_bytes(transport_stats[:envelopes_memory])}</td></tr>
                <tr><td>Log Events</td><td>#{transport_stats[:log_events]}</td><td>#{format_bytes(transport_stats[:log_events_memory])}</td></tr>
            </table>
        </div>

        #{generate_memory_breakdown_section(main_report)}
        #{generate_allocation_analysis_section(main_report)}
        #{generate_retention_analysis_section(main_report)}
        #{generate_file_analysis_section(main_report)}
        #{generate_recommendations_section(main_report, iterations)}

    </body>
    </html>
  HTML
end



def generate_memory_breakdown_section(report)
  <<~HTML
    <div class="section">
        <h2>🧠 Memory Breakdown by Object Type</h2>
        <table class="memory-table">
            <tr><th>Object Type</th><th>Allocated</th><th>Retained</th><th>Memory Allocated</th><th>Memory Retained</th></tr>
            #{report.allocated_objects_by_class.first(10).map do |klass, count|
              retained_count = report.retained_objects_by_class[klass] || 0
              allocated_mem = report.allocated_memory_by_class[klass] || 0
              retained_mem = report.retained_memory_by_class[klass] || 0
              "<tr><td>#{klass}</td><td>#{count}</td><td>#{retained_count}</td><td>#{format_bytes(allocated_mem)}</td><td>#{format_bytes(retained_mem)}</td></tr>"
            end.join("\n")}
        </table>
    </div>
  HTML
end

def generate_allocation_analysis_section(report)
  top_allocations = report.allocated_objects_by_location.first(15)

  <<~HTML
    <div class="section">
        <h2>📍 Top Memory Allocation Locations</h2>
        <p>These are the code locations that allocated the most objects during logging operations:</p>
        <table class="memory-table">
            <tr><th>Location</th><th>Objects Allocated</th><th>Memory</th><th>% of Total</th></tr>
            #{top_allocations.map do |location, count|
              memory = report.allocated_memory_by_location[location] || 0
              percentage = (count.to_f / report.total_allocated * 100).round(2)
              "<tr><td class='code-location'>#{location}</td><td>#{count}</td><td>#{format_bytes(memory)}</td><td>#{percentage}%</td></tr>"
            end.join("\n")}
        </table>
    </div>
  HTML
end

def generate_retention_analysis_section(report)
  top_retentions = report.retained_objects_by_location.first(15)

  <<~HTML
    <div class="section">
        <h2>🔒 Memory Retention Analysis</h2>
        <p>These locations are retaining objects in memory (potential memory leaks):</p>
        #{if top_retentions.empty?
          '<div class="success">✅ No significant memory retention detected! This is good news.</div>'
          else
          <<~RETENTION_HTML
            <div class="warning">⚠️ Memory retention detected at the following locations:</div>
            <table class="memory-table">
                <tr><th>Location</th><th>Objects Retained</th><th>Memory Retained</th><th>% of Total</th></tr>
                #{top_retentions.map do |location, count|
                  memory = report.retained_memory_by_location[location] || 0
                  percentage = (count.to_f / report.total_retained * 100).round(2)
                  "<tr><td class='code-location'>#{location}</td><td>#{count}</td><td>#{format_bytes(memory)}</td><td>#{percentage}%</td></tr>"
                end.join("\n")}
            </table>
          RETENTION_HTML
          end}
    </div>
  HTML
end

def generate_file_analysis_section(report)
  file_allocations = {}
  report.allocated_objects_by_location.each do |location, count|
    file = location.split(':').first
    file_allocations[file] = (file_allocations[file] || 0) + count
  end

  top_files = file_allocations.sort_by { |_, count| -count }.first(10)

  <<~HTML
    <div class="section">
        <h2>📁 Memory Usage by File</h2>
        <p>Files contributing most to memory allocation:</p>
        <table class="memory-table">
            <tr><th>File</th><th>Objects Allocated</th><th>% of Total</th></tr>
            #{top_files.map do |file, count|
              percentage = (count.to_f / report.total_allocated * 100).round(2)
              file_display = file.include?('sentry') ? "<strong>#{file}</strong>" : file
              "<tr><td class='code-location'>#{file_display}</td><td>#{count}</td><td>#{percentage}%</td></tr>"
            end.join("\n")}
        </table>
    </div>
  HTML
end

def generate_recommendations_section(report, iterations)
  retention_rate = (report.total_retained.to_f / report.total_allocated * 100).round(2)
  memory_per_iteration = report.total_allocated_memsize / iterations

  recommendations = []

  if retention_rate > 10
    recommendations << "🔴 High memory retention rate (#{retention_rate}%) detected. Review the retention analysis section for potential memory leaks."
  elsif retention_rate > 5
    recommendations << "🟡 Moderate memory retention rate (#{retention_rate}%). Monitor for potential memory growth over time."
  else
    recommendations << "🟢 Low memory retention rate (#{retention_rate}%) - this is good!"
  end

  if memory_per_iteration > 1024 * 1024 # 1MB per iteration
    recommendations << "🔴 High memory usage per iteration (#{format_bytes(memory_per_iteration)}). Consider optimizing log message creation."
  elsif memory_per_iteration > 512 * 1024 # 512KB per iteration
    recommendations << "🟡 Moderate memory usage per iteration (#{format_bytes(memory_per_iteration)}). Room for optimization."
  else
    recommendations << "🟢 Reasonable memory usage per iteration (#{format_bytes(memory_per_iteration)})."
  end

  # Check for string allocations
  string_allocations = report.allocated_objects_by_class['String'] || 0
  if string_allocations > report.total_allocated * 0.3
    recommendations << "🔴 High string allocation rate (#{((string_allocations.to_f / report.total_allocated) * 100).round(1)}%). Consider string optimization techniques."
  end

  # Check for hash allocations
  hash_allocations = report.allocated_objects_by_class['Hash'] || 0
  if hash_allocations > report.total_allocated * 0.2
    recommendations << "🟡 Significant hash allocation (#{((hash_allocations.to_f / report.total_allocated) * 100).round(1)}%). Review hash usage in logging."
  end

  <<~HTML
    <div class="section">
        <h2>💡 Performance Recommendations</h2>
        <div style="margin: 15px 0;">
            #{recommendations.map { |rec| "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 3px;'>#{rec}</div>" }.join("\n")}
        </div>

        <h3>General Optimization Tips:</h3>
        <ul>
            <li><strong>String Optimization:</strong> Use frozen strings and avoid unnecessary string concatenation</li>
            <li><strong>Hash Optimization:</strong> Pre-allocate hashes when possible and avoid creating temporary hashes</li>
            <li><strong>Log Sampling:</strong> Consider implementing log sampling for high-volume applications</li>
            <li><strong>Async Logging:</strong> Use background processing for non-critical log events</li>
            <li><strong>Memory Monitoring:</strong> Run this benchmark regularly to track memory usage trends</li>
        </ul>

        <h3>Sentry-Specific Tips:</h3>
        <ul>
            <li><strong>Log Levels:</strong> Use appropriate log levels to reduce unnecessary processing</li>
            <li><strong>Structured Data:</strong> Minimize the size of structured data passed to log events</li>
            <li><strong>Before Send Hooks:</strong> Use before_send_log callbacks to filter out unnecessary events</li>
            <li><strong>Transport Configuration:</strong> Optimize transport settings for your use case</li>
        </ul>
    </div>

    <div class="section">
        <h2>📋 Raw Memory Profile Data</h2>
        <details>
            <summary>Click to view detailed memory profiler output</summary>
            <pre>#{report.pretty_print(to_file: nil, detailed_report: false)}</pre>
        </details>
    </div>
  HTML
end
